[gd_scene load_steps=26 format=3 uid="uid://30hpl7wxn3e7"]

[ext_resource type="Script" uid="uid://b3vumlej270lw" path="res://src/battle_ground/battle_ground.gd" id="1_gildm"]
[ext_resource type="PackedScene" uid="uid://bexl2b5q5oopf" path="res://src/ui/performance_monitor.tscn" id="2_roawh"]
[ext_resource type="PackedScene" uid="uid://cyn32jhnmyvlc" path="res://src/ui/equipment_ui.tscn" id="3_mftls"]
[ext_resource type="PackedScene" uid="uid://bhdq3r65eieyf" path="res://src/ui/credits_display.tscn" id="4_uyst7"]
[ext_resource type="PackedScene" uid="uid://hjusjc3ewh33" path="res://src/scenes/common/pause_menu.tscn" id="5_2wrul"]
[ext_resource type="Script" uid="uid://dpxqtnxm6umfg" path="res://src/globals/damage_number_pool.gd" id="6_50pgy"]
[ext_resource type="Script" uid="uid://lpvq04imgbf3" path="res://src/equipment/hit_effect/hit_effect_manager.gd" id="7_xtwav"]
[ext_resource type="PackedScene" uid="uid://c6g8qj0v1w7vj" path="res://src/entities/player/player.tscn" id="8_y6r64"]
[ext_resource type="Script" uid="uid://cutwsom3s04lv" path="res://src/equipment/equipment_manager.gd" id="9_16l56"]
[ext_resource type="Script" path="res://src/equipment/mod/mod_manager.gd" id="10_xnryc"]
[ext_resource type="Script" path="res://src/equipment/mod/mod_resource.gd" id="11_f8xh4"]
[ext_resource type="Resource" uid="uid://cldn7p8omriu3" path="res://src/equipment/mod/resources/test_weapon_boost_mod.tres" id="12_3m4jv"]
[ext_resource type="Script" uid="uid://bc3m45ixxioyb" path="res://src/entities/player/player_stats_manager.gd" id="13_fw67o"]
[ext_resource type="Script" uid="uid://bl0upi7hb4mj2" path="res://src/entities/player/damage_type_stats.gd" id="14_vy127"]
[ext_resource type="Script" path="res://src/equipment/equipment_slot_manager.gd" id="15_oht37"]
[ext_resource type="PackedScene" uid="uid://dog00wdohu7m1" path="res://src/battle_ground/room/room.tscn" id="16_mftls"]
[ext_resource type="Script" uid="uid://xeqrhmtkouk4" path="res://src/battle_ground/room_manager.gd" id="16_uyst7"]

[sub_resource type="Resource" id="Resource_4u850"]
script = ExtResource("14_vy127")

[sub_resource type="Resource" id="Resource_y16n8"]
script = ExtResource("14_vy127")

[sub_resource type="Resource" id="Resource_2ckkw"]
script = ExtResource("14_vy127")

[sub_resource type="Resource" id="Resource_lf3bo"]
script = ExtResource("14_vy127")

[sub_resource type="Resource" id="Resource_0lrsq"]
script = ExtResource("14_vy127")

[sub_resource type="Resource" id="Resource_6yet0"]
script = ExtResource("14_vy127")

[sub_resource type="Resource" id="Resource_xxjrd"]
script = ExtResource("14_vy127")

[sub_resource type="Resource" id="Resource_37cy8"]
script = ExtResource("14_vy127")

[node name="BattleGround" type="Node2D"]
script = ExtResource("1_gildm")

[node name="Camera2D" type="Camera2D" parent="."]

[node name="Pool" type="Node2D" parent="."]
unique_name_in_owner = true
script = ExtResource("6_50pgy")

[node name="UI" type="CanvasLayer" parent="."]

[node name="PerformanceMonitor" parent="UI" instance=ExtResource("2_roawh")]
offset_left = -1480.0
offset_right = -1280.0

[node name="EquipmentUI" parent="UI" instance=ExtResource("3_mftls")]

[node name="CreditsDisplay" parent="UI" instance=ExtResource("4_uyst7")]

[node name="PauseMenu" parent="UI" instance=ExtResource("5_2wrul")]
visible = false

[node name="Systems" type="Node" parent="."]

[node name="HitEffectManager" type="Node" parent="Systems"]
unique_name_in_owner = true
script = ExtResource("7_xtwav")

[node name="EquipmentManager" type="Node" parent="Systems"]
unique_name_in_owner = true
script = ExtResource("9_16l56")
metadata/_custom_type_script = "uid://cutwsom3s04lv"

[node name="ModManager" type="Node" parent="Systems"]
unique_name_in_owner = true
script = ExtResource("10_xnryc")
default_mods = Array[ExtResource("11_f8xh4")]([ExtResource("12_3m4jv")])

[node name="PlayerStatsManager" type="Node" parent="Systems"]
unique_name_in_owner = true
script = ExtResource("13_fw67o")
base_stats = Dictionary[String, float]({
"armor": 0.0,
"bounce_count": 0.0,
"health_regen_rate": 0.0,
"magazine_capacity_bonus": 0.0,
"max_health_bonus": 0.0,
"move_speed_multiplier": 1.0,
"multishot_count": 0.0,
"pierce_count": 0.0,
"projectile_speed_multiplier": 1.0
})
damage_type_stats = Dictionary[int, ExtResource("14_vy127")]({
0: SubResource("Resource_4u850"),
1: SubResource("Resource_y16n8"),
2: SubResource("Resource_2ckkw"),
3: SubResource("Resource_lf3bo"),
4: SubResource("Resource_0lrsq"),
5: SubResource("Resource_6yet0"),
6: SubResource("Resource_xxjrd"),
7: SubResource("Resource_37cy8")
})
metadata/_custom_type_script = "uid://bc3m45ixxioyb"

[node name="EquipmentSlotManager" type="Node" parent="Systems"]
unique_name_in_owner = true
script = ExtResource("15_oht37")

[node name="Player" parent="." instance=ExtResource("8_y6r64")]
unique_name_in_owner = true
position = Vector2(-2, -6)
collision_mask = 34
max_health = 1000
speed = 600.0

[node name="RoomManager" type="Node" parent="."]
script = ExtResource("16_uyst7")
metadata/_custom_type_script = "uid://xeqrhmtkouk4"

[node name="Room" parent="RoomManager" instance=ExtResource("16_mftls")]
